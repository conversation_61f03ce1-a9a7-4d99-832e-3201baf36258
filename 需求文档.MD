# 星河写作 - 需求文档

这是需求文档，记录了项目的架构、功能需求和开发计划。

## 1. 项目结构

### 1.1 技术栈
- **前端框架**: Next.js 14 (App Router)
- **开发语言**: TypeScript
- **UI框架**: Tailwind CSS (吉卜力工作室艺术风格)
- **状态管理**: Zustand
- **数据存储**: IndexedDB (本地) + Supabase (云端)
- **用户认证**: Supabase Auth
- **AI服务**: 多模型支持 (Gemini、Claude等)
- **文件存储**: MinIO
- **富文本编辑**: TipTap

### 1.2 目录结构
```
/src
├── app/                    # Next.js App Router页面
│   ├── api/               # API路由
│   │   ├── ai/           # AI相关API
│   │   ├── auth/         # 认证API
│   │   ├── billing/      # 计费API
│   │   ├── files/        # 文件管理API
│   │   ├── prompt/       # 提示词API
│   │   ├── user/         # 用户API
│   │   └── works/        # 作品API
│   ├── auth/             # 认证相关页面
│   ├── works/            # 作品管理页面
│   ├── prompts/          # 提示词管理页面
│   ├── chat/             # AI对话页面
│   └── knowledgebase/    # 知识库页面
├── components/            # 可复用组件
│   ├── auth/             # 认证组件
│   ├── chat/             # 对话组件
│   ├── works/            # 作品组件
│   ├── prompts/          # 提示词组件
│   ├── knowledgebase/    # 知识库组件
│   └── common/           # 通用组件
├── contexts/             # React Context
├── data/                 # 数据层
│   └── database/         # 数据库操作
├── hooks/                # 自定义Hooks
├── lib/                  # 工具库
│   ├── billing/          # 计费服务
│   └── utils/            # 工具函数
├── services/             # 业务服务
├── store/                # Zustand状态管理
├── types/                # TypeScript类型定义
└── utils/                # 工具函数
    └── supabase/         # Supabase客户端
```

## 2. 项目关键文件的作用

### 2.1 核心配置文件
- **package.json**: 项目依赖和脚本配置，定义了项目名称"zhuguang1.0"
- **next.config.mjs**: Next.js配置，禁用严格模式和构建错误检查
- **tailwind.config.ts**: Tailwind CSS配置，定义吉卜力风格的颜色系统和动画
- **tsconfig.json**: TypeScript编译配置

### 2.2 布局和样式文件
- **src/app/layout.tsx**: 根布局组件，集成认证、导航、AI对话等全局功能
- **src/app/globals.css**: 全局样式，定义吉卜力风格的颜色变量和组件样式
- **src/styles/typing-cursor.css**: 打字机效果样式

### 2.3 数据层文件
- **src/data/database/config.ts**: 数据库配置，定义IndexedDB数据库结构
- **src/data/database/core/**: 数据库核心操作（连接、迁移、操作）
- **src/data/database/repositories/**: 数据仓库层，各实体的CRUD操作
- **src/data/database/types/**: 数据类型定义

### 2.4 认证和用户管理
- **src/utils/supabase/**: Supabase客户端配置（浏览器端、服务端、中间件）
- **src/hooks/useAuth.ts**: 认证Hook，提供登录、注册、登出功能
- **src/components/auth/**: 认证相关组件（登录、注册、保护路由等）

### 2.5 AI服务文件
- **src/lib/AIserver.ts**: AI服务接口，统一管理多个AI模型
- **src/app/api/ai/stream/route.ts**: AI流式API路由，处理AI对话请求
- **src/lib/billing/**: 计费服务，管理token消费和用户余额

### 2.6 核心功能模块
- **src/app/works/**: 作品管理页面和编辑器
- **src/components/works/**: 作品相关组件（编辑器、AI助手、章节管理等）
- **src/app/prompts/**: 提示词管理页面
- **src/components/prompts/**: 提示词相关组件
- **src/app/knowledgebase/**: 知识库管理页面
- **src/components/knowledgebase/**: 知识库相关组件

### 2.7 全局功能
- **src/contexts/**: React Context（导航、AI对话）
- **src/components/chat/**: 全局AI对话功能组件
- **src/store/**: Zustand状态管理
- **middleware.ts**: Next.js中间件，处理认证和路由保护

## 3. 需求计划

### 3.1 覆盖层字编辑器显示大纲设定角色知识库内容

#### 需求描述
当AI编辑器覆盖层已经打开时，点击大纲、设定、角色、知识库条目，内容会在覆盖层的字编辑器中显示，就像现在点击章节一样。覆盖层未打开时保持现有功能不变。

#### 技术要点
当AI覆盖层打开时，点击侧边栏的大纲、设定、角色、知识库条目，内容会在覆盖层子编辑器中显示。只需要扩展AI覆盖层子编辑器支持这四种内容类型。

#### 当前状态分析

**现有功能**：
- AI覆盖层面板（`AIOverlayPanel.tsx`）目前只支持章节内容的显示和编辑
- 侧边栏（`Sidebar.tsx`）中四个模块都有完整的点击处理机制
- 主页面（`page.tsx`）通过`editMode`状态管理不同内容类型的显示

**数据结构**：
- 大纲(Outline)：{id, title, content, order, workId, createdAt, updatedAt}
- 设定(Setting)：{id, title, content, category, order, workId, createdAt, updatedAt}
- 角色(Character)：{id, name, gender, personality, background, workId, createdAt, updatedAt}
- 知识库(Knowledge)：{id, title, content, workId, tags, createdAt, updatedAt}

#### 技术实现计划

##### 步骤1：扩展AI覆盖层面板支持多种内容类型
**文件**: `src/components/works/AIOverlayPanel.tsx`

**关键修改点**：
1. **扩展组件Props接口**：
   ```typescript
   interface AIOverlayPanelProps {
     // 现有props...

     // 新增四个模块的数据和状态
     outlines?: Outline[];
     activeOutline?: string | null;
     settings?: Setting[];
     activeSetting?: string | null;
     characters?: Character[];
     activeCharacter?: string | null;
     knowledges?: Knowledge[];
     activeKnowledge?: number | null;

     // 内容类型控制
     contentType?: 'chapter' | 'outline' | 'setting' | 'character' | 'knowledge';
   }
   ```

2. **添加内容类型状态管理**：
   ```typescript
   const [contentType, setContentType] = useState<'chapter' | 'outline' | 'setting' | 'character' | 'knowledge'>('chapter');
   const [currentEditContent, setCurrentEditContent] = useState('');
   ```

3. **实现内容获取逻辑**：
   ```typescript
   const getCurrentContent = useCallback(() => {
     switch (contentType) {
       case 'chapter':
         return chapters[activeChapter]?.content || '';
       case 'outline':
         return outlines?.find(o => o.id === activeOutline)?.content || '';
       case 'setting':
         return settings?.find(s => s.id === activeSetting)?.content || '';
       case 'character':
         const character = characters?.find(c => c.id === activeCharacter);
         return character ? `性格：${character.personality}\n\n背景：${character.background}` : '';
       case 'knowledge':
         return knowledges?.find(k => k.id === activeKnowledge)?.content || '';
       default:
         return '';
     }
   }, [contentType, chapters, activeChapter, outlines, activeOutline, settings, activeSetting, characters, activeCharacter, knowledges, activeKnowledge]);
   ```

4. **实现内容保存逻辑**：
   ```typescript
   const handleContentSave = useCallback(async (content: string) => {
     try {
       switch (contentType) {
         case 'chapter':
           // 现有章节保存逻辑
           break;
         case 'outline':
           const outline = outlines?.find(o => o.id === activeOutline);
           if (outline) {
             const { updateOutline } = await import('@/data');
             await updateOutline({ ...outline, content, updatedAt: new Date() });
           }
           break;
         case 'setting':
           const setting = settings?.find(s => s.id === activeSetting);
           if (setting) {
             const { updateSetting } = await import('@/data');
             await updateSetting({ ...setting, content, updatedAt: new Date() });
           }
           break;
         case 'character':
           const character = characters?.find(c => c.id === activeCharacter);
           if (character) {
             // 解析内容，分离性格和背景
             const lines = content.split('\n\n');
             const personality = lines[0]?.replace('性格：', '') || '';
             const background = lines[1]?.replace('背景：', '') || '';
             const { updateCharacter } = await import('@/data');
             await updateCharacter({ ...character, personality, background, updatedAt: new Date() });
           }
           break;
         case 'knowledge':
           const knowledge = knowledges?.find(k => k.id === activeKnowledge);
           if (knowledge) {
             const { updateArchive } = await import('@/data');
             await updateArchive({ ...knowledge, content, updatedAt: new Date() });
           }
           break;
       }
       onDataChange?.(); // 通知父组件数据更新
     } catch (error) {
       console.error('保存内容失败:', error);
     }
   }, [contentType, outlines, activeOutline, settings, activeSetting, characters, activeCharacter, knowledges, activeKnowledge, onDataChange]);
   ```

5. **修改字编辑器区域**：
   ```typescript
   // 在字编辑器上方添加内容类型指示器
   <div className="flex justify-between items-center mb-2 text-sm text-gray-500">
     <span>
       {contentType === 'chapter' && chapters[activeChapter] ? `编辑: ${chapters[activeChapter].title}` :
        contentType === 'outline' && outlines?.find(o => o.id === activeOutline) ? `编辑大纲: ${outlines.find(o => o.id === activeOutline)?.title}` :
        contentType === 'setting' && settings?.find(s => s.id === activeSetting) ? `编辑设定: ${settings.find(s => s.id === activeSetting)?.title}` :
        contentType === 'character' && characters?.find(c => c.id === activeCharacter) ? `编辑角色: ${characters.find(c => c.id === activeCharacter)?.name}` :
        contentType === 'knowledge' && knowledges?.find(k => k.id === activeKnowledge) ? `编辑知识: ${knowledges.find(k => k.id === activeKnowledge)?.title}` :
        '选择内容进行编辑'}
     </span>
   </div>
   ```

##### 步骤2：修改主页面组件集成
**文件**: `src/app/works/[id]/page.tsx`

**关键修改点**：
1. **添加AI覆盖层内容类型状态**：
   ```typescript
   const [overlayContentType, setOverlayContentType] = useState<'chapter' | 'outline' | 'setting' | 'character' | 'knowledge'>('chapter');
   const [overlayActiveOutline, setOverlayActiveOutline] = useState<string | null>(null);
   const [overlayActiveSetting, setOverlayActiveSetting] = useState<string | null>(null);
   const [overlayActiveCharacter, setOverlayActiveCharacter] = useState<string | null>(null);
   const [overlayActiveKnowledge, setOverlayActiveKnowledge] = useState<number | null>(null);
   ```

2. **实现四个模块在AI覆盖层显示的处理函数**：
   ```typescript
   // 大纲在覆盖层显示处理
   const handleOutlineClickInOverlay = (outlineId: string) => {
     if (showAIOverlay) {
       setOverlayContentType('outline');
       setOverlayActiveOutline(outlineId);
       setOverlayActiveSetting(null);
       setOverlayActiveCharacter(null);
       setOverlayActiveKnowledge(null);
     } else {
       handleOutlineClick(outlineId); // 保持原有功能
     }
   };

   // 设定在覆盖层显示处理
   const handleSettingClickInOverlay = (settingId: string) => {
     if (showAIOverlay) {
       setOverlayContentType('setting');
       setOverlayActiveSetting(settingId);
       setOverlayActiveOutline(null);
       setOverlayActiveCharacter(null);
       setOverlayActiveKnowledge(null);
     } else {
       handleSettingClick(settingId); // 保持原有功能
     }
   };

   // 角色在覆盖层显示处理
   const handleCharacterClickInOverlay = (characterId: string) => {
     if (showAIOverlay) {
       setOverlayContentType('character');
       setOverlayActiveCharacter(characterId);
       setOverlayActiveOutline(null);
       setOverlayActiveSetting(null);
       setOverlayActiveKnowledge(null);
     } else {
       handleCharacterClick(characterId); // 保持原有功能
     }
   };

   // 知识库在覆盖层显示处理
   const handleKnowledgeClickInOverlay = (knowledgeId: number) => {
     if (showAIOverlay) {
       setOverlayContentType('knowledge');
       setOverlayActiveKnowledge(knowledgeId);
       setOverlayActiveOutline(null);
       setOverlayActiveSetting(null);
       setOverlayActiveCharacter(null);
     } else {
       handleKnowledgeClick(knowledgeId); // 保持原有功能
     }
   };
   ```

3. **扩展AIOverlayPanel的props传递**：
   ```typescript
   <AIOverlayPanel
     isOpen={showAIOverlay}
     onClose={() => setShowAIOverlay(false)}
     onInsertToEditor={onInsertToEditor}
     currentContent={selectedText}
     chapters={chapters}
     activeChapter={activeChapter}
     initialPromptType={selectedText ? 'ai_polishing' : 'ai_writing'}
     workId={workId}
     defaultIsDescending={isDescending}
     onDataChange={() => {
       loadWork();
     }}
     // 新增四个模块的数据传递
     outlines={outlines}
     activeOutline={overlayActiveOutline}
     settings={settings}
     activeSetting={overlayActiveSetting}
     characters={characters}
     activeCharacter={overlayActiveCharacter}
     knowledges={knowledges}
     activeKnowledge={overlayActiveKnowledge}
     contentType={overlayContentType}
   />
   ```

##### 步骤3：修改侧边栏点击处理
**文件**: `src/components/Sidebar.tsx`

**关键修改点**：
1. **扩展SidebarProps接口**：
   ```typescript
   interface SidebarProps {
     // 现有props...

     // 新增AI覆盖层相关回调
     onOutlineClickInOverlay?: (outlineId: string) => void;
     onSettingClickInOverlay?: (settingId: string) => void;
     onCharacterClickInOverlay?: (characterId: string) => void;
     onKnowledgeClickInOverlay?: (knowledgeId: number) => void;
     isAIOverlayOpen?: boolean;
   }
   ```

2. **修改四个模块的点击处理**：
   ```typescript
   // 大纲点击处理
   <div
     className={`group relative flex items-center py-2 px-3 cursor-pointer transition-colors duration-200 rounded-lg mx-1 my-0.5 ${activeOutline === outline.id ? 'bg-[rgba(90,157,107,0.1)]' : 'hover:bg-[rgba(90,157,107,0.05)]'}`}
     onClick={() => {
       if (isAIOverlayOpen && onOutlineClickInOverlay) {
         onOutlineClickInOverlay(outline.id);
       } else if (onOutlineClick) {
         onOutlineClick(outline.id);
       }
     }}
   >
   ```

   类似地修改设定、角色、知识库的点击处理。

3. **更新主页面中Sidebar组件的调用**：
   ```typescript
   <Sidebar
     // 现有props...
     onOutlineClickInOverlay={handleOutlineClickInOverlay}
     onSettingClickInOverlay={handleSettingClickInOverlay}
     onCharacterClickInOverlay={handleCharacterClickInOverlay}
     onKnowledgeClickInOverlay={handleKnowledgeClickInOverlay}
     isAIOverlayOpen={showAIOverlay}
   />
   ```

#### 实现优先级
1. **高优先级**：步骤1 - 扩展AI覆盖层面板核心功能
2. **中优先级**：步骤2 - 主页面组件集成
3. **低优先级**：步骤3 - 侧边栏交互优化

#### 测试要点
1. AI覆盖层打开时，点击四个模块条目应在覆盖层显示内容
2. AI覆盖层未打开时，保持原有功能不变
3. 不同内容类型的保存功能正常工作
4. 角色内容的性格和背景字段正确解析和保存
5. 内容切换时状态管理正确
